import React, { useEffect, useState, useCallback } from "react";
import { UserWrapper } from "@/components/UserWrapper";
import InteractiveButton from "@/components/InteractiveButton/InteractiveButton";
import { Link, useNavigate } from "react-router-dom";
import { useSDK } from "@/hooks/useSDK";
import { MkdLoader } from "@/components/MkdLoader";
import { PaginationBar } from "@/components/PaginationBar";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { PlusIcon, EditIcon, TrashIcon } from "@/assets/svgs";

interface IListing {
  id: number;
  name: string;
  price: string;
  status: string;
  description?: string;
  image?: string;
  category?: string;
  type?: string;
  created_at: string;
  updated_at: string;
  buyer?: string;
  sold_date?: string;
  delivery_status?: string;
}

interface IPagination {
  page: number;
  limit: number;
  total: number;
  num_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

const UserListingsListPage = () => {
  const navigate = useNavigate();
  const [listings, setListings] = useState<IListing[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<IPagination | null>(null);
  const [activeTab, setActiveTab] = useState<"all" | "sold">("all");
  const [showFilters, setShowFilters] = useState(true);
  const [filters, setFilters] = useState({
    page: 1,
    limit: 6,
    search: "",
    status: "All Statuses",
    type: "All",
    category: "All Categories",
    dateAdded: "Any Time",
    priceRange: { min: "", max: "" },
  });
  const [searchInput, setSearchInput] = useState("");

  const { sdk } = useSDK();

  // Mock data for demonstration - replace with actual API call
  const mockListings: IListing[] = [
    {
      id: 1,
      name: "Wireless Headphones",
      price: "129.99",
      status: "active",
      description: "Noise-cancelling wireless headphones with 30h battery life",
      image: "/api/placeholder/300/200",
      category: "Electronics",
      type: "Item",
      created_at: "2024-04-10",
      updated_at: "2024-04-10",
      buyer: "",
      sold_date: "Added Apr 10, '25",
      delivery_status: "Active",
    },
    {
      id: 2,
      name: "Mountain Bike",
      price: "420.00",
      status: "expired",
      description: "21-speed mountain bike, great condition, recently serviced",
      image: "/api/placeholder/300/200",
      category: "Sports",
      type: "Item",
      created_at: "2024-02-20",
      updated_at: "2024-02-20",
      buyer: "",
      sold_date: "Added Feb 20, '25",
      delivery_status: "Expired 2 days ago",
    },
    {
      id: 3,
      name: "Graphic Design Services",
      price: "75.00",
      status: "sponsored",
      description:
        "Professional graphic design services for logos, branding, etc.",
      image: "/api/placeholder/300/200",
      category: "Services",
      type: "Service",
      created_at: "2024-04-05",
      updated_at: "2024-04-05",
      buyer: "",
      sold_date: "Added Apr 5, '25",
      delivery_status: "Sponsored",
    },
    {
      id: 4,
      name: "Antique Wooden Desk",
      price: "350.00",
      status: "draft",
      description: "Early 1900s oak writing desk with original hardware",
      image: "/api/placeholder/300/200",
      category: "Furniture",
      type: "Item",
      created_at: "2024-04-18",
      updated_at: "2024-04-18",
      buyer: "",
      sold_date: "Saved Apr 18, '25",
      delivery_status: "Not published yet",
    },
    {
      id: 5,
      name: "Graphic Design Services",
      price: "75.00",
      status: "sponsored",
      description:
        "Professional graphic design services for logos, branding, etc.",
      image: "/api/placeholder/300/200",
      category: "Services",
      type: "Service",
      created_at: "2024-04-05",
      updated_at: "2024-04-05",
      buyer: "",
      sold_date: "Added Apr 5, '25",
      delivery_status: "Sponsored",
    },
    {
      id: 6,
      name: "Antique Wooden Desk",
      price: "350.00",
      status: "draft",
      description: "Early 1900s oak writing desk with original hardware",
      image: "/api/placeholder/300/200",
      category: "Furniture",
      type: "Item",
      created_at: "2024-04-15",
      updated_at: "2024-04-15",
      buyer: "",
      sold_date: "Not published yet",
      delivery_status: "Saved Apr 15, '25",
    },
    {
      id: 7,
      name: "Mountain Bike",
      price: "420.00",
      status: "expired",
      description: "21-speed mountain bike, great condition, recently serviced",
      image: "/api/placeholder/300/200",
      category: "Sports",
      type: "Item",
      created_at: "2024-02-20",
      updated_at: "2024-02-20",
      buyer: "",
      sold_date: "Added Feb 20, '25",
      delivery_status: "Expired 2 days ago",
    },
    {
      id: 8,
      name: "Graphic Design Services",
      price: "75.00",
      status: "sponsored",
      description:
        "Professional graphic design services for logos, branding, etc.",
      image: "/api/placeholder/300/200",
      category: "Services",
      type: "Service",
      created_at: "2024-04-05",
      updated_at: "2024-04-05",
      buyer: "",
      sold_date: "Added Apr 5, '25",
      delivery_status: "Sponsored",
    },
    {
      id: 9,
      name: "Antique Wooden Desk",
      price: "350.00",
      status: "draft",
      description: "Early 1900s oak writing desk with original hardware",
      image: "/api/placeholder/300/200",
      category: "Furniture",
      type: "Item",
      created_at: "2024-04-15",
      updated_at: "2024-04-15",
      buyer: "",
      sold_date: "Not published yet",
      delivery_status: "Saved Apr 15, '25",
    },
  ];

  const fetchListings = async () => {
    setLoading(true);
    try {
      // For now, use mock data - replace with actual API call later
      setTimeout(() => {
        let filteredListings = mockListings;

        // Filter by tab
        if (activeTab === "sold") {
          filteredListings = filteredListings.filter((listing) =>
            [
              "pending_shipping",
              "in_transit",
              "shipped",
              "booked",
              "waiting_confirmation",
              "completed",
            ].includes(listing.status)
          );
        } else {
          // For "all" tab, show all listings
          filteredListings = mockListings;
        }

        // Apply search filter
        if (filters.search) {
          filteredListings = filteredListings.filter(
            (listing) =>
              listing.name
                .toLowerCase()
                .includes(filters.search.toLowerCase()) ||
              listing.description
                ?.toLowerCase()
                .includes(filters.search.toLowerCase())
          );
        }

        // Apply status filter
        if (filters.status !== "All Statuses") {
          filteredListings = filteredListings.filter((listing) => {
            if (filters.status === "Active") return listing.status === "active";
            if (filters.status === "Draft") return listing.status === "draft";
            if (filters.status === "Expired")
              return listing.status === "expired";
            if (filters.status === "Sponsored")
              return listing.status === "sponsored";
            return true;
          });
        }

        // Apply type filter
        if (filters.type !== "All") {
          filteredListings = filteredListings.filter(
            (listing) => listing.type === filters.type
          );
        }

        // Apply category filter
        if (filters.category !== "All Categories") {
          filteredListings = filteredListings.filter(
            (listing) => listing.category === filters.category
          );
        }

        setListings(filteredListings);
        setPagination({
          page: filters.page,
          limit: filters.limit,
          total: filteredListings.length,
          num_pages: Math.ceil(filteredListings.length / filters.limit),
          has_next:
            filters.page < Math.ceil(filteredListings.length / filters.limit),
          has_prev: filters.page > 1,
        });
        setLoading(false);
      }, 500);
    } catch (error) {
      console.error("Error fetching listings:", error);
      setListings([]);
      setPagination(null);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchListings();
  }, [filters.page, filters.search, activeTab]);

  const handleSearch = useCallback(() => {
    setFilters((prev) => ({
      ...prev,
      search: searchInput,
      page: 1,
    }));
  }, [searchInput]);

  const handlePageChange = (page: number) => {
    setFilters((prev) => ({ ...prev, page }));
  };

  const handleTabChange = (tab: "all" | "sold") => {
    setActiveTab(tab);
    setFilters((prev) => ({ ...prev, page: 1 }));
  };

  const handleFilterChange = (filterType: string, value: string | object) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: value,
      page: 1,
    }));
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500 text-white";
      case "expired":
        return "bg-red-500 text-white";
      case "sponsored":
        return "bg-yellow-500 text-black";
      case "draft":
        return "bg-gray-500 text-white";
      case "pending_shipping":
        return "bg-orange-500 text-white";
      case "in_transit":
        return "bg-blue-500 text-white";
      case "shipped":
        return "bg-green-500 text-white";
      case "booked":
        return "bg-purple-500 text-white";
      case "waiting_confirmation":
        return "bg-yellow-500 text-white";
      case "completed":
        return "bg-green-600 text-white";
      default:
        return "bg-gray-500 text-white";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "active":
        return "Active";
      case "expired":
        return "Expired 2 days ago";
      case "sponsored":
        return "Sponsored";
      case "draft":
        return "Draft";
      case "pending_shipping":
        return "Pending Shipping";
      case "in_transit":
        return "In Transit";
      case "shipped":
        return "Shipped";
      case "booked":
        return "Booked";
      case "waiting_confirmation":
        return "Waiting Confirmation";
      case "completed":
        return "Completed";
      default:
        return status;
    }
  };

  return (
    <UserWrapper>
      <div className="p-6 min-h-screen">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-white">My Listings</h1>
          <Link
            to="/user/listings/add"
            className="bg-[#E63946] text-white px-4 py-2 rounded-md hover:bg-[#d32f2f] flex items-center gap-2 text-sm font-medium"
          >
            + Add New Listing
          </Link>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="flex space-x-0">
            <button
              onClick={() => handleTabChange("all")}
              className={`px-6 py-3 text-sm font-medium rounded-l-md border ${
                activeTab === "all"
                  ? "bg-[#0F2C59] text-white border-[#0F2C59]"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              }`}
            >
              All Listings
            </button>
            <button
              onClick={() => handleTabChange("sold")}
              className={`px-6 py-3 text-sm font-medium rounded-r-md border-t border-r border-b ${
                activeTab === "sold"
                  ? "bg-[#E63946] text-white border-[#E63946]"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              }`}
            >
              Sold & Action Needed
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mb-6">
          <div className="flex gap-4 mb-4">
            <div className="flex-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                  <svg
                    className="w-4 h-4 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Search by item name, category, or tag"
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && handleSearch()}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                />
              </div>
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="px-4 py-2 border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 flex items-center gap-2 text-sm font-medium"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.121A1 1 0 013 6.414V4z"
                />
              </svg>
              Filters
            </button>
            <button className="px-4 py-2 border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 flex items-center gap-2 text-sm font-medium">
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"
                />
              </svg>
              Sort
            </button>
          </div>

          {/* Filter Dropdowns */}
          {showFilters && (
            <div className="grid grid-cols-4 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange("status", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                >
                  <option>All Statuses</option>
                  <option>Active</option>
                  <option>Draft</option>
                  <option>Expired</option>
                  <option>Sponsored</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Type
                </label>
                <select
                  value={filters.type}
                  onChange={(e) => handleFilterChange("type", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                >
                  <option>All</option>
                  <option>Item</option>
                  <option>Service</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  value={filters.category}
                  onChange={(e) =>
                    handleFilterChange("category", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                >
                  <option>All Categories</option>
                  <option>Electronics</option>
                  <option>Sports</option>
                  <option>Services</option>
                  <option>Furniture</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date Added
                </label>
                <select
                  value={filters.dateAdded}
                  onChange={(e) => handleFilterChange("dateAdded", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                >
                  <option>Any Time</option>
                  <option>Last 7 days</option>
                  <option>Last 30 days</option>
                  <option>Last 3 months</option>
                </select>
              </div>
            </div>

            {/* Price Range Row */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Price Range (eBa$)
                </label>
                <div className="flex gap-2">
                  <input
                    type="text"
                    placeholder="Min"
                    value={filters.priceRange.min}
                    onChange={(e) => handleFilterChange("priceRange", { ...filters.priceRange, min: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                  />
                  <input
                    type="text"
                    placeholder="Max"
                    value={filters.priceRange.max}
                    onChange={(e) => handleFilterChange("priceRange", { ...filters.priceRange, max: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Listings Grid */}
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <MkdLoader />
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
              {listings.map((listing) => (
                <div
                  key={listing.id}
                  className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
                >
                  {/* Status Badge */}
                  <div className="relative">
                    <img
                      src={listing.image || "/api/placeholder/300/200"}
                      alt={listing.name}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute top-3 left-3">
                      <span className="text-xs font-medium text-gray-600 bg-white px-2 py-1 rounded">
                        {listing.type}
                      </span>
                    </div>
                    <div className="absolute top-3 right-3">
                      <span
                        className={`text-xs font-medium px-2 py-1 rounded ${getStatusBadgeColor(listing.status)}`}
                      >
                        {getStatusText(listing.status)}
                      </span>
                    </div>
                    {listing.status === "sponsored" && (
                      <div className="absolute top-3 right-3">
                        <span className="text-xs font-medium px-2 py-1 rounded bg-yellow-400 text-black">
                          ⭐ Sponsored
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Card Content */}
                  <div className="p-4">
                    <h3 className="font-semibold text-[#0F2C59] mb-1 text-base">
                      {listing.name}
                    </h3>
                    <p className="text-[#E63946] font-bold text-lg mb-2">
                      eBa$ {listing.price}{listing.type === "Service" ? "/hr" : ""}
                    </p>
                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                      {listing.description}
                    </p>

                    <div className="text-xs text-gray-500 mb-3">
                      <div className="mb-1">{listing.category}</div>
                      <div className="flex items-center gap-4 mb-1">
                        <span>Quantity: 1 available | 0 sold</span>
                      </div>
                      <div className="flex items-center gap-4">
                        <span>👁 15 views</span>
                        <span>❓ 3 inquiries</span>
                        <span>• {listing.sold_date}</span>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2 mb-3">
                      <button className="flex items-center gap-1 text-xs text-gray-600 hover:text-gray-800">
                        ✏️ Edit
                      </button>
                      <button className="flex items-center gap-1 text-xs text-gray-600 hover:text-gray-800">
                        🗑️ Delete
                      </button>
                      <button className="flex items-center gap-1 text-xs text-gray-600 hover:text-gray-800">
                        👁️ View
                      </button>
                    </div>

                    {/* Additional Action Buttons for specific statuses */}
                    {listing.status === "pending_shipping" && (
                      <button className="w-full mt-2 bg-[#0F2C59] text-white text-xs py-2 px-3 rounded hover:bg-[#1a3a6b] flex items-center justify-center gap-1">
                        <svg
                          className="w-3 h-3"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        Provide Shipping Info
                      </button>
                    )}

                    {listing.status === "waiting_confirmation" && (
                      <button className="w-full mt-2 bg-orange-500 text-white text-xs py-2 px-3 rounded hover:bg-orange-600 flex items-center justify-center gap-1">
                        <svg
                          className="w-3 h-3"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        Waiting for buyer confirmation...
                      </button>
                    )}

                    {listing.status === "completed" && (
                      <button className="w-full mt-2 bg-green-500 text-white text-xs py-2 px-3 rounded hover:bg-green-600 flex items-center justify-center gap-1">
                        <svg
                          className="w-3 h-3"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                        Mark as Completed
                      </button>
                    )}

                    {/* Delivery Status */}
                    <div className="mt-3 pt-3 border-t border-gray-100">
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-gray-500">
                          {listing.type === "Service"
                            ? "Other local delivery Status:"
                            : "EBA Delivery Status:"}
                        </span>
                        <span
                          className={`font-medium ${
                            listing.status === "in_transit"
                              ? "text-blue-600"
                              : listing.status === "shipped"
                                ? "text-green-600"
                                : "text-gray-600"
                          }`}
                        >
                          {listing.status === "in_transit"
                            ? "In Transit"
                            : listing.status === "shipped"
                              ? "Shipped"
                              : listing.status === "completed"
                                ? "Client left a 5-star review"
                                : ""}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {listings.length === 0 && (
              <div className="text-center py-12">
                <div className="text-gray-400 text-lg mb-2">
                  No listings found
                </div>
                <p className="text-gray-500 mb-4">
                  Create your first listing to get started
                </p>
                <Link
                  to="/user/listings/add"
                  className="bg-[#E63946] text-white px-4 py-2 rounded-md hover:bg-[#d32f2f]"
                >
                  Add New Listing
                </Link>
              </div>
            )}
          </>
        )}

        {/* Pagination */}
        {pagination && pagination.num_pages > 1 && (
          <div className="flex justify-center items-center gap-2 mt-8">
            <span className="text-sm text-gray-600">
              Showing{" "}
              {Math.min(
                (pagination.page - 1) * pagination.limit + 1,
                pagination.total
              )}{" "}
              of {pagination.total} listings
            </span>
            <div className="flex gap-1 ml-4">
              <button
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={!pagination.has_prev}
                className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                &lt;
              </button>
              {Array.from(
                { length: Math.min(5, pagination.num_pages) },
                (_, i) => {
                  const pageNum = i + 1;
                  return (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`px-3 py-1 text-sm border rounded ${
                        pagination.page === pageNum
                          ? "bg-[#E63946] text-white border-[#E63946]"
                          : "border-gray-300 hover:bg-gray-50"
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                }
              )}
              <button
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={!pagination.has_next}
                className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                &gt;
              </button>
            </div>
          </div>
        )}
      </div>
    </UserWrapper>
  );
};

export default UserListingsListPage;
